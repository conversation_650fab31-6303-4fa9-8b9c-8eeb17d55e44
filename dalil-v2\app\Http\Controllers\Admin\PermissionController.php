<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PermissionController extends Controller
{
    /**
     * عرض قائمة الصلاحيات
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            return $this->getPermissionsData($request);
        }

        return view('admin.permissions.index');
    }

    /**
     * جلب بيانات الصلاحيات عبر AJAX
     */
    public function getPermissionsData(Request $request)
    {
        $query = Permission::withCount('roles');

        // البحث
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('display_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('group', 'like', "%{$search}%");
            });
        }

        // فلترة حسب المجموعة
        if ($request->filled('group')) {
            $query->where('group', $request->group);
        }

        // الترتيب
        $sortBy = $request->get('sort_by', 'group');
        $sortDir = $request->get('sort_dir', 'asc');
        
        if ($sortBy === 'group') {
            $query->orderBy('group', $sortDir)->orderBy('display_name', 'asc');
        } else {
            $query->orderBy($sortBy, $sortDir);
        }

        $permissions = $query->paginate($request->get('per_page', 25));

        return response()->json([
            'success' => true,
            'data' => $permissions->items(),
            'pagination' => [
                'current_page' => $permissions->currentPage(),
                'last_page' => $permissions->lastPage(),
                'per_page' => $permissions->perPage(),
                'total' => $permissions->total(),
                'from' => $permissions->firstItem(),
                'to' => $permissions->lastItem(),
            ],
            'links' => $permissions->appends(request()->query())->links()->render()
        ]);
    }

    /**
     * عرض نموذج إنشاء صلاحية جديدة
     */
    public function create()
    {
        $groups = Permission::distinct()->pluck('group')->filter()->sort();
        return view('admin.permissions.create', compact('groups'));
    }

    /**
     * حفظ صلاحية جديدة
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'group' => 'required|string|max:100',
        ], [
            'name.required' => 'اسم الصلاحية مطلوب',
            'name.unique' => 'اسم الصلاحية موجود مسبقاً',
            'display_name.required' => 'الاسم المعروض مطلوب',
            'group.required' => 'مجموعة الصلاحية مطلوبة'
        ]);

        try {
            Permission::create([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'group' => $request->group,
            ]);

            return redirect()->route('admin.permissions.index')
                ->with('success', 'تم إنشاء الصلاحية بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إنشاء الصلاحية')
                ->withInput();
        }
    }

    /**
     * عرض تفاصيل الصلاحية
     */
    public function show(Permission $permission)
    {
        $permission->load('roles');
        return view('admin.permissions.show', compact('permission'));
    }

    /**
     * عرض نموذج تعديل الصلاحية
     */
    public function edit(Permission $permission)
    {
        $groups = Permission::distinct()->pluck('group')->filter()->sort();
        return view('admin.permissions.edit', compact('permission', 'groups'));
    }

    /**
     * تحديث الصلاحية
     */
    public function update(Request $request, Permission $permission)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'group' => 'required|string|max:100',
        ], [
            'name.required' => 'اسم الصلاحية مطلوب',
            'name.unique' => 'اسم الصلاحية موجود مسبقاً',
            'display_name.required' => 'الاسم المعروض مطلوب',
            'group.required' => 'مجموعة الصلاحية مطلوبة'
        ]);

        try {
            $permission->update([
                'name' => $request->name,
                'display_name' => $request->display_name,
                'description' => $request->description,
                'group' => $request->group,
            ]);

            return redirect()->route('admin.permissions.index')
                ->with('success', 'تم تحديث الصلاحية بنجاح');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء تحديث الصلاحية')
                ->withInput();
        }
    }

    /**
     * حذف الصلاحية
     */
    public function destroy(Permission $permission)
    {
        try {
            // التحقق من عدم وجود أدوار مرتبطة بهذه الصلاحية
            if ($permission->roles()->count() > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف هذه الصلاحية لأنها مرتبطة بأدوار'
                ], 400);
            }

            $permission->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الصلاحية بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الصلاحية'
            ], 500);
        }
    }

    /**
     * إنشاء صلاحيات افتراضية للنظام
     */
    public function createDefaultPermissions()
    {
        $defaultPermissions = [
            // إدارة المستخدمين
            ['name' => 'users.view', 'display_name' => 'عرض المستخدمين', 'group' => 'المستخدمين'],
            ['name' => 'users.create', 'display_name' => 'إنشاء مستخدم', 'group' => 'المستخدمين'],
            ['name' => 'users.edit', 'display_name' => 'تعديل المستخدمين', 'group' => 'المستخدمين'],
            ['name' => 'users.delete', 'display_name' => 'حذف المستخدمين', 'group' => 'المستخدمين'],
            ['name' => 'users.suspend', 'display_name' => 'تعليق المستخدمين', 'group' => 'المستخدمين'],

            // إدارة الأدوار
            ['name' => 'roles.view', 'display_name' => 'عرض الأدوار', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'roles.create', 'display_name' => 'إنشاء دور', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'roles.edit', 'display_name' => 'تعديل الأدوار', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'roles.delete', 'display_name' => 'حذف الأدوار', 'group' => 'الأدوار والصلاحيات'],

            // إدارة الصلاحيات
            ['name' => 'permissions.view', 'display_name' => 'عرض الصلاحيات', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'permissions.create', 'display_name' => 'إنشاء صلاحية', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'permissions.edit', 'display_name' => 'تعديل الصلاحيات', 'group' => 'الأدوار والصلاحيات'],
            ['name' => 'permissions.delete', 'display_name' => 'حذف الصلاحيات', 'group' => 'الأدوار والصلاحيات'],

            // إدارة الإعلانات
            ['name' => 'advertisements.view', 'display_name' => 'عرض الإعلانات', 'group' => 'الإعلانات'],
            ['name' => 'advertisements.approve', 'display_name' => 'الموافقة على الإعلانات', 'group' => 'الإعلانات'],
            ['name' => 'advertisements.reject', 'display_name' => 'رفض الإعلانات', 'group' => 'الإعلانات'],
            ['name' => 'advertisements.delete', 'display_name' => 'حذف الإعلانات', 'group' => 'الإعلانات'],

            // إدارة الباقات
            ['name' => 'packages.view', 'display_name' => 'عرض الباقات', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'packages.create', 'display_name' => 'إنشاء باقة', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'packages.edit', 'display_name' => 'تعديل الباقات', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'packages.delete', 'display_name' => 'حذف الباقات', 'group' => 'الباقات والاشتراكات'],

            // إدارة الاشتراكات
            ['name' => 'subscriptions.view', 'display_name' => 'عرض الاشتراكات', 'group' => 'الباقات والاشتراكات'],
            ['name' => 'subscriptions.manage', 'display_name' => 'إدارة الاشتراكات', 'group' => 'الباقات والاشتراكات'],

            // إدارة البيانات الأساسية
            ['name' => 'basic-data.view', 'display_name' => 'عرض البيانات الأساسية', 'group' => 'البيانات الأساسية'],
            ['name' => 'basic-data.manage', 'display_name' => 'إدارة البيانات الأساسية', 'group' => 'البيانات الأساسية'],

            // إدارة الإعدادات
            ['name' => 'settings.view', 'display_name' => 'عرض الإعدادات', 'group' => 'الإعدادات'],
            ['name' => 'settings.edit', 'display_name' => 'تعديل الإعدادات', 'group' => 'الإعدادات'],

            // التقارير والإحصائيات
            ['name' => 'reports.view', 'display_name' => 'عرض التقارير', 'group' => 'التقارير والإحصائيات'],
            ['name' => 'analytics.view', 'display_name' => 'عرض الإحصائيات', 'group' => 'التقارير والإحصائيات'],
        ];

        try {
            DB::beginTransaction();

            foreach ($defaultPermissions as $permission) {
                Permission::firstOrCreate(
                    ['name' => $permission['name']],
                    $permission
                );
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الصلاحيات الافتراضية بنجاح'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الصلاحيات الافتراضية'
            ], 500);
        }
    }
}
