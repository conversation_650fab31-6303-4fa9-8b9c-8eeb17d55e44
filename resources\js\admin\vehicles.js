/**
 * Page User List
 */

'use strict';
import { deleteRecord } from '../ajax';

// Datatable (jquery)
$(function () {
  // ajax setup
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
  });
  function loadData(vehicle = '', type = '', lodeType = true, loadAll = true, loadSize = true) {
    $.ajax({
      url: baseUrl + 'admin/settings/vehicles/data',
      type: 'GET',
      data: { vehicle: vehicle, type: type },
      success: function (response) {
        // Update statistics
        updateStatistics(response.data);

        if (loadAll) {
          var vehicles = response.data.vehicles
            .map(
              (vehicle, index) => `
          <tr>
            <td class="text-center"><strong>${index + 1}</strong></td>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar avatar-sm me-2">
                  <div class="avatar-initial bg-label-primary rounded">
                    <i class="ti ti-car"></i>
                  </div>
                </div>
                <div>
                  <h6 class="mb-0">${vehicle.name}</h6>
                  <small class="text-muted">${vehicle.en_name}</small>
                </div>
              </div>
            </td>
            <td>
              <span class="badge bg-label-info">${vehicle.types} ${__('types')}</span>
            </td>
            <td class="text-center">
              <button class="btn btn-action btn-sm btn-outline-primary edit-v-record"
                data-id="${vehicle.id}" data-name="${vehicle.name}" data-enname="${vehicle.en_name}"
                title="${__('Edit')}">
                <i class="ti ti-edit"></i>
              </button>
              <button class="btn btn-action btn-sm btn-outline-danger delete-v-record"
                data-id="${vehicle.id}" data-name="${vehicle.name}"
                title="${__('Delete')}">
                <i class="ti ti-trash"></i>
              </button>
            </td>
          </tr>`
            )
            .join('');

          if (response.data.vehicles.length === 0) {
            vehicles = `<tr>
              <td colspan="4" class="text-center text-muted">
                <div class="empty-state">
                  <i class="ti ti-car-off"></i>
                  <h6>${__('No Vehicles Found')}</h6>
                  <p>${__('Start by adding your first vehicle above')}</p>
                </div>
              </td>
            </tr>`;
          }
          $('#vehicle-table').html(vehicles);

          var types = response.data.types
            .map(
              (type, index) => `
          <tr>
            <td class="text-center"><strong>${index + 1}</strong></td>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar avatar-sm me-2">
                  <div class="avatar-initial bg-label-success rounded">
                    <i class="ti ti-car"></i>
                  </div>
                </div>
                <span class="fw-medium">${type.vehicle}</span>
              </div>
            </td>
            <td>
              <div>
                <h6 class="mb-0">${type.name}</h6>
                <small class="text-muted">${type.en_name}</small>
              </div>
            </td>
            <td>
              <span class="badge bg-label-warning">${type.sizes} ${__('sizes')}</span>
            </td>
            <td class="text-center">
              <button class="btn btn-action btn-sm btn-outline-primary edit-t-record"
                data-id="${type.id}" data-name="${type.name}" data-enname="${type.en_name}" data-vehicle="${type.vehicle_id}"
                title="${__('Edit')}">
                <i class="ti ti-edit"></i>
              </button>
              <button class="btn btn-action btn-sm btn-outline-danger delete-t-record"
                data-id="${type.id}" data-name="${type.name}"
                title="${__('Delete')}">
                <i class="ti ti-trash"></i>
              </button>
            </td>
          </tr>`
            )
            .join('');

          if (response.data.types.length === 0) {
            types = `<tr>
                <td colspan="5" class="text-center text-muted">
                  <div class="empty-state">
                    <i class="ti ti-category-off"></i>
                    <h6>${__('No Vehicle Types Found')}</h6>
                    <p>${__('Add vehicle types to organize your fleet')}</p>
                  </div>
                </td>
              </tr>`;
          }
          $('#types-table').html(types);

          var sizes = response.data.sizes
            .map(
              (size, index) => `
          <tr>
            <td class="text-center"><strong>${index + 1}</strong></td>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar avatar-sm me-2">
                  <div class="avatar-initial bg-label-success rounded">
                    <i class="ti ti-car"></i>
                  </div>
                </div>
                <span class="fw-medium">${size.vehicle}</span>
              </div>
            </td>
            <td>
              <span class="badge bg-label-info">${size.type}</span>
            </td>
            <td>
              <div class="d-flex align-items-center">
                <i class="ti ti-ruler me-2 text-muted"></i>
                <span class="fw-medium">${size.name}</span>
              </div>
            </td>
            <td class="text-center">
              <button class="btn btn-action btn-sm btn-outline-primary edit-s-record"
                data-id="${size.id}" data-name="${size.name}" data-type="${size.type_id}" data-vehicle="${size.vehicle_id}"
                title="${__('Edit')}">
                <i class="ti ti-edit"></i>
              </button>
              <button class="btn btn-action btn-sm btn-outline-danger delete-s-record"
                data-id="${size.id}" data-name="${size.name}"
                title="${__('Delete')}">
                <i class="ti ti-trash"></i>
              </button>
            </td>
          </tr>`
            )
            .join('');

          if (response.data.sizes.length === 0) {
            sizes = `<tr>
                  <td colspan="5" class="text-center text-muted">
                    <div class="empty-state">
                      <i class="ti ti-dimensions-off"></i>
                      <h6>${__('No Vehicle Sizes Found')}</h6>
                      <p>${__('Add vehicle sizes to complete your fleet configuration')}</p>
                    </div>
                  </td>
                </tr>`;
          }
          $('#sizes-table').html(sizes);
        }
        // توليد القوائم المنسدلة
        var vehicle_options = ` <option value="">-- ${__('Select vehicle')} --</option>`;
        vehicle_options += response.data.vehicles
          .map(
            option => `
          <option value="${option.id}">${option.name} - ${option.en_name}</option>
        `
          )
          .join('');
        if (lodeType) {
          $('.vehicle-type-vehicle').html(vehicle_options);
        }

        var vehicle_type_options = ` <option value="">-- ${__('select vehicle type')} --</option>`;
        vehicle_type_options += response.data.types
          .map(
            option => `
          <option value="${option.id}"> ${option.name} - ${option.en_name}</option>
        `
          )
          .join('');

        if (loadSize) {
          $('.vehicle-sizes-vehicle').html(vehicle_type_options);
        }

        var vehicle_sizes_options = ` <option value="">-- ${__('select vehicle Size')} --</option>`;
        vehicle_sizes_options += response.data.sizes
          .map(
            size => `
          <option value="${size.id}"> ${size.name}</option>
        `
          )
          .join('');

        if (loadSize) {
          $('#size-vehicle').html(vehicle_type_options);
        }
      }
    });
  }

  // Function to update statistics
  function updateStatistics(data) {
    // Animate counter updates
    animateCounter('#vehicles-count', data.vehicles.length);
    animateCounter('#types-count', data.types.length);
    animateCounter('#sizes-count', data.sizes.length);
  }

  // Function to animate counter
  function animateCounter(selector, targetValue) {
    const element = $(selector);
    const currentValue = parseInt(element.text()) || 0;

    if (currentValue !== targetValue) {
      $({ counter: currentValue }).animate(
        { counter: targetValue },
        {
          duration: 1000,
          easing: 'swing',
          step: function () {
            element.text(Math.ceil(this.counter));
          },
          complete: function () {
            element.text(targetValue);
          }
        }
      );
    }
  }

  loadData();

  $(document).on('change', '#type-vehicle-flitter', function () {
    var vehicle = $(this).val();
    loadData(vehicle, '', false);
  });

  $(document).on('change', '#vehicle-size-vehicle', function () {
    var vehicle = $(this).val();
    loadData(vehicle, '', false, false);
  });

  $(document).on('change', '#size-vehicle-flitter', function () {
    var vehicle = $(this).val();
    loadData(vehicle, '', false, false);
  });

  $(document).on('change', '#size-type-flitter', function () {
    var vehicle = $(this).val();
    loadData(vehicle, vehicle, false, true, false);
  });

  document.addEventListener('formSubmitted', function (event) {
    $('.form_submit').trigger('reset');
    $('#vehicle-id').val('');
    $('#vehicle-type-id').val('');
    $('#vehicle-size-id').val('');

    loadData();
  });
  document.addEventListener('deletedSuccess', function (event) {
    loadData();
  });

  $(document).on('click', '.edit-v-record', function () {
    var Id = $(this).data('id');
    var name = $(this).data('name');
    var en_name = $(this).data('enname');

    $('#vehicle-name').val(name);
    $('#vehicle-en-name').val(en_name);
    $('#vehicle-id').val(Id);
  });

  $(document).on('click', '.edit-t-record', function () {
    var Id = $(this).data('id');
    var name = $(this).data('name');
    var en_name = $(this).data('enname');
    var vehicle = $(this).data('vehicle');

    $('#vehicle-type-name').val(name);
    $('#vehicle-type-en-name').val(en_name);
    $('#vehicle-type-vehicle').val(vehicle);
    $('#vehicle-type-id').val(Id);
  });

  $(document).on('click', '.edit-s-record', function () {
    var Id = $(this).data('id');
    var name = $(this).data('name');
    var vehicle = $(this).data('vehicle');
    var type = $(this).data('type');

    $('#vehicle-size-name').val(name);
    $('#vehicle-size-type').val(type);
    $('#vehicle-size-vehicle').val(vehicle);
    $('#vehicle-size-id').val(Id);
  });

  $(document).on('click', '.delete-v-record', function () {
    let url = baseUrl + 'admin/settings/vehicles/delete/' + $(this).data('id');
    deleteRecord($(this).data('name'), url);
  });

  $(document).on('click', '.delete-t-record', function () {
    let url = baseUrl + 'admin/settings/vehicles/type/delete/' + $(this).data('id');
    deleteRecord($(this).data('name'), url);
  });

  $(document).on('click', '.delete-s-record', function () {
    let url = baseUrl + 'admin/settings/vehicles/size/delete/' + $(this).data('id');
    deleteRecord($(this).data('name'), url);
  });
});
