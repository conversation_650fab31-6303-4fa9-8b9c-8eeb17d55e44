<!DOCTYPE html>
<html>
<head>
    <title>Test AjaxTable</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="http://127.0.0.1:8002/js/admin/ajax-table.js"></script>
</head>
<body>
    <h1>اختبار AjaxTable</h1>
    <div id="result"></div>

    <script>
    $(document).ready(function() {
        console.log('Document ready');
        console.log('jQuery available:', typeof $);
        console.log('AjaxTable available:', typeof AjaxTable);
        
        if (typeof AjaxTable !== 'undefined') {
            $('#result').html('✅ AjaxTable متاح!');
        } else {
            $('#result').html('❌ AjaxTable غير متاح!');
        }
    });
    </script>
</body>
</html>
