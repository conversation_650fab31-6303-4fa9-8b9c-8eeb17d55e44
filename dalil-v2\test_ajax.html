<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>اختبار AJAX للصلاحيات</h1>
    <button id="test-btn">اختبار الطلب</button>
    <div id="result"></div>

    <script>
    $('#test-btn').click(function() {
        $('#result').html('جاري التحميل...');
        
        $.ajax({
            url: 'http://127.0.0.1:8002/admin/permissions-data',
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log('Success:', response);
                $('#result').html('<pre>' + JSON.stringify(response, null, 2) + '</pre>');
            },
            error: function(xhr, status, error) {
                console.log('Error:', xhr, status, error);
                $('#result').html('خطأ: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText);
            }
        });
    });
    </script>
</body>
</html>
