<?php $__env->startSection('title', __('Templates')); ?>

<?php $__env->startSection('vendor-style'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.scss', 'resources/assets/vendor/libs/datatables-buttons-bs5/buttons.bootstrap5.scss', 'resources/assets/vendor/libs/select2/select2.scss', 'resources/assets/vendor/libs/@form-validation/form-validation.scss', 'resources/assets/vendor/libs/animate-css/animate.scss', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.scss']); ?>

    <style>
        .sortable-ghost {
            background: #f0f8ff;
            border: 2px dashed #007bff;
            opacity: 0.7;
        }

        .drag-handle {
            cursor: grab;
        }

        .drag-handle:active {
            cursor: grabbing;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/libs/moment/moment.js', 'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js', 'resources/assets/vendor/libs/select2/select2.js', 'resources/assets/vendor/libs/@form-validation/popular.js', 'resources/assets/vendor/libs/@form-validation/bootstrap5.js', 'resources/assets/vendor/libs/@form-validation/auto-focus.js', 'resources/assets/vendor/libs/cleavejs/cleave.js', 'resources/assets/vendor/libs/cleavejs/cleave-phone.js', 'resources/assets/vendor/libs/sweetalert2/sweetalert2.js']); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/assets/vendor/libs/sortablejs/sortable.js']); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-script'); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/admin/templates.js']); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/admin/pricing_template.js']); ?>

    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/ajax.js']); ?>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/model.js']); ?>
    <script>
        let fieldIndex = <?php echo e($data->fields->count()); ?>;
        const formFields = <?php echo json_encode($data->fields, 15, 512) ?>;
        const templateId = <?php echo e($data->id); ?>;
        const geoFences = <?php echo json_encode($geofences, 15, 512) ?>;
    </script>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('templates-isactive'); ?>
    active
<?php $__env->stopSection(); ?>



<?php $__env->startSection('content'); ?>

    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="card-title mb-2"><?php echo e(__('Settings')); ?> | <?php echo e(__('Templates')); ?> |
                <span class="bg-info text-white px-2 rounded">
                    <?php echo e($data->name); ?></span>
            </h5>
            <p><?php echo e($data->description); ?></p>
            <input type="hidden" class="form-control" id="template_id" value="<?php echo e($data->id); ?>">

            <div class="mt-6 ">
                <span class="id-error text-danger text-error"></span>
                <span class="fields-error text-danger text-error"></span>
                <table class="table mb-6">
                    <thead>
                        <tr>
                            <th></th>
                            <th><?php echo e(__('name')); ?></th>
                            <th><?php echo e(__('label')); ?></th>
                            <th><?php echo e(__('driver can')); ?></th>
                            <th><?php echo e(__('customer can')); ?></th>
                            <th><?php echo e(__('type')); ?></th>
                            <th><?php echo e(__('value')); ?></th>
                            <th><?php echo e(__('require')); ?></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody id="fields_table">
                        <?php $__currentLoopData = $data->fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $fieldType = isset($field->type) ? $field->type : '';
                                $selectValues =
                                    $fieldType === 'select' && !empty($field->value)
                                        ? json_decode($field->value, true)
                                        : [];
                            ?>

                            <tr class="form-field-row" data-id="<?php echo e($field->id); ?>">
                                <td class="drag-handle" style="cursor: grab;">☰</td>
                                <td>
                                    <input type="text" class="form-control field-name-input"
                                        value="<?php echo e($field->name); ?>">
                                    <span class="field-<?php echo e($key); ?>-name-error text-danger text-error"></span>

                                </td>
                                <td>
                                    <input type="text" class="form-control field-label-input"
                                        value="<?php echo e($field->label); ?>">
                                    <span class="field-<?php echo e($key); ?>-label-error text-danger text-error"></span>

                                </td>
                                <td>
                                    <select class="form-control field-manager">
                                        <option value="hidden" <?php echo e($field->driver_can == 'hidden' ? 'selected' : ''); ?>>
                                            Hidden
                                        </option>
                                        <option value="read" <?php echo e($field->driver_can == 'read' ? 'selected' : ''); ?>>Read
                                            Only</option>
                                        <option value="write" <?php echo e($field->driver_can == 'write' ? 'selected' : ''); ?>>Read &
                                            Write</option>
                                    </select>
                                    <span class="field-<?php echo e($key); ?>-driver_can-error text-danger text-error"></span>

                                </td>
                                <td>
                                    <select class="form-control field-customer-can-select">
                                        <option value="hidden" <?php echo e($field->customer_can == 'hidden' ? 'selected' : ''); ?>>
                                            Hidden</option>
                                        <option value="read" <?php echo e($field->customer_can == 'read' ? 'selected' : ''); ?>>Read
                                            Only</option>
                                        <option value="write" <?php echo e($field->customer_can == 'write' ? 'selected' : ''); ?>>Read
                                            & Write</option>
                                    </select>
                                    <span
                                        class="field-<?php echo e($key); ?>-customer_can-error text-danger text-error"></span>

                                </td>
                                <td>
                                    <select class="form-control field-type-select">
                                        <option value="string" <?php echo e($fieldType == 'string' ? 'selected' : ''); ?>>text</option>
                                        <option value="number" <?php echo e($fieldType == 'number' ? 'selected' : ''); ?>>number
                                        </option>
                                        <option value="email" <?php echo e($fieldType == 'email' ? 'selected' : ''); ?>>email
                                        </option>
                                        <option value="date" <?php echo e($fieldType == 'date' ? 'selected' : ''); ?>>date</option>
                                        <option value="file" <?php echo e($fieldType == 'file' ? 'selected' : ''); ?>>file</option>
                                        <option value="file_expiration_date"
                                            <?php echo e($fieldType == 'file_expiration_date' ? 'selected' : ''); ?>>file with
                                            expiration date</option>
                                        <option value="file_with_text"
                                            <?php echo e($fieldType == 'file_with_text' ? 'selected' : ''); ?>>file with text/number
                                        </option>
                                        <option value="image" <?php echo e($fieldType == 'image' ? 'selected' : ''); ?>>image</option>
                                        <option value="url" <?php echo e($fieldType == 'url' ? 'selected' : ''); ?>>url</option>
                                        <option value="select" <?php echo e($fieldType == 'select' ? 'selected' : ''); ?>>select
                                        </option>
                                    </select>
                                    <span class="field-<?php echo e($key); ?>-type-error text-danger text-error"></span>

                                </td>
                                <td>
                                    <?php
                                        $placeholder = 'Default value or placeholder text';
                                        if ($field->type === 'file_expiration_date') {
                                            $placeholder =
                                                'Label for expiration date (e.g., "Expiry Date", "Valid Until")';
                                        } elseif ($field->type === 'file_with_text') {
                                            $placeholder =
                                                'Label for text field (e.g., "License Number", "Document ID")';
                                        } elseif ($field->type === 'select') {
                                            $placeholder = 'Select options managed below';
                                        }
                                    ?>
                                    <input type="text" class="form-control field-value-input"
                                        value="<?php echo e($fieldType == 'select' ? '' : $field->value); ?>"
                                        placeholder="<?php echo e($placeholder); ?>">
                                    <span class="field-<?php echo e($key); ?>-value-error text-danger text-error"></span>

                                </td>
                                <td>
                                    <select class="form-control field-required-select">
                                        <option value="0" <?php echo e(!$field->required ? 'selected' : ''); ?>>NO</option>
                                        <option value="1" <?php echo e($field->required ? 'selected' : ''); ?>>YES</option>
                                    </select>
                                    <span class="field-<?php echo e($key); ?>-required-error text-danger text-error"></span>
                                </td>
                                <td><button class="btn btn-sm btn-icon text-danger remove-field"><i
                                            class="ti ti-trash"></i></button>
                                </td>
                            </tr>

                            <?php if($field->type == 'select'): ?>
                                <tr class="select-values-table connected-row" data-id="<?php echo e($field->id); ?>">
                                    <td colspan="4">
                                        <div class="p-2 border rounded  shadow-sm">
                                            <h6 class="text-primary">🔗 <?php echo e(__('Select Values')); ?></h6>
                                            <table class="table ">
                                                <thead>
                                                    <tr>
                                                        <th><?php echo e(__('Value')); ?></th>
                                                        <th><?php echo e(__('Display Name')); ?></th>
                                                        <th><?php echo e(__('Action')); ?></th>
                                                    </tr>
                                                </thead>
                                                <tbody class="select-values-body">
                                                    <?php $__currentLoopData = json_decode($field->value ?? '[]', true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <tr>
                                                            <td><input type="text"
                                                                    class="form-control select-value-input"
                                                                    value="<?php echo e($option['value']); ?>"></td>
                                                            <td><input type="text" class="form-control select-name-input"
                                                                    value="<?php echo e($option['name']); ?>"></td>
                                                            <td class="text-center">
                                                                <button type="button"
                                                                    class="btn btn-sm btn-icon text-danger remove-select-value"><i
                                                                        class="ti ti-trash"></i></button>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </tbody>
                                            </table>
                                            <button type="button"
                                                class="btn btn-sm btn-icon text-primary add-select-value"> <i
                                                    class="ti ti-plus me-0 me-sm-1 ti-xs"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>





                <button id="add_field" class="btn "> <i class="ti ti-plus me-0 me-sm-1 ti-xs"></i>
                    <?php echo e(__('More')); ?> </button>
                <button id="save_template" class="btn btn-primary"><?php echo e(__('save')); ?></button>



            </div>

        </div>
    </div>

    <div class="col-md">

        <div id="accordionCustomIcon" class="accordion mt-4 accordion-custom-button">
            <div class="accordion-item">
                <h2 class="accordion-header text-body d-flex justify-content-between" id="accordionCustomIconOne">
                    <button type="button" class="accordion-button collapsed" data-bs-toggle="collapse"
                        data-bs-target="#accordionCustomIcon-1" aria-controls="accordionCustomIcon-1">
                        <i class="ri-bar-chart-2-line me-2 ri-20px"></i>
                        <h5> Tasks Pricing</h5>
                    </button>
                </h2>

                <div id="accordionCustomIcon-1" class="accordion-collapse collapse" data-bs-parent="#accordionCustomIcon">
                    <div class="accordion-body">
                        <button class="add-new btn btn-primary waves-effect waves-light mb-5 mx-4" data-bs-toggle="modal"
                            data-bs-target="#submitModal">
                            <i class="ti ti-plus me-0 me-sm-1 ti-xs"></i>
                            <span class="d-none d-sm-inline-block"> <?php echo e(__('Add Pricing Module')); ?></span>
                        </button>
                        <div class="card-datatable table-responsive">
                            <table class="table  datatables-pricing">
                                <thead class="border-top">
                                    <tr>
                                        <th></th> <!-- للعمود control -->
                                        <th>#</th> <!-- للـ fake_id -->
                                        <th>Role name</th>
                                        <th>Created at</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>


                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade " id="submitModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modelTitle"><?php echo e(__('Add Pricing Module')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form class="add-new-user pt-0 form_submit" method="POST"
                    action="<?php echo e(route('settings.templates.pricing.store')); ?>">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <input type="hidden" name="form_id" value="<?php echo e($data->id); ?>">
                        <input type="hidden" name="id" id="pricing_id">
                        <!-- Rule Name -->
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-8">
                                    <label class="form-label">* <?php echo e(__('Rule Name')); ?></label>
                                    <input type="text" name="rule_name" class="form-control" placeholder="Role Name">
                                    <span class="rule_name-error text-danger text-error"></span>

                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">* <?php echo e(__('Set Decimal Places')); ?></label>
                                    <input type="number" name="decimal_places" class="form-control"
                                        placeholder="Set Decimal Places For config the price" value="2">
                                    <span class="decimal_places-error text-danger text-error"></span>
                                </div>
                            </div>

                        </div>



                        <!-- Customers Selection -->
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text"><strong><?php echo e(__('Customers Selections')); ?></strong></div>
                            </div>

                            <div class="mb-4">
                                <input type="checkbox" id="allCustomers" name="all_customers" value="true"
                                    class="form-check-input" checked>
                                <label for="allCustomers"><?php echo e(__('Apply to All Customers')); ?></label>
                                <span class="all_customers-error text-danger text-error"></span>

                            </div>

                            <div class="row">
                                <!-- Tags -->
                                <div class="col-md-6">
                                    <label for=""><?php echo e(__('Use customers tags')); ?> </label>
                                    <input type="checkbox" id="useTags" name="use_tags" value="true"
                                        class="form-check-input mb-2">
                                    <select class="form-select select2-tags" name="tags[]" multiple id="tagsSelect"
                                        disabled>
                                        <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($val->id); ?>"><?php echo e($val->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <span class="use_tags-error text-danger text-error"></span>
                                    <span class="tags-error text-danger text-error"></span>

                                </div>

                                <!-- Specific Customers -->
                                <div class="col-md-6">
                                    <label for=""><?php echo e(__('Use Specific customers')); ?> </label>
                                    <input type="checkbox" id="useCustomers" name="use_customers" value="true"
                                        class="form-check-input mb-2">
                                    <select class="form-select select2-customers" name="customers[]" multiple
                                        id="customersSelect" disabled>
                                        <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($val->id); ?>"><?php echo e($val->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <span class="use_customers-error text-danger text-error"></span>
                                    <span class="customers-error text-danger text-error"></span>

                                </div>
                            </div>
                        </div>

                        <!-- Vehicle Sizes -->
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text"><strong><?php echo e(__('Vehicles Selections')); ?></strong></div>
                            </div>
                            <!-- vehicle tabs start -->
                            <div class="nav-align-top mb-6">
                                <ul class="nav nav-tabs mb-4" role="tablist">
                                    <?php $__currentLoopData = $vehicle; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="nav-item">
                                            <button type="button" class="nav-link <?php echo e($key === 0 ? 'active' : ''); ?>"
                                                role="tab" data-bs-toggle="tab"
                                                data-bs-target="#vehicle-<?php echo e($val->id); ?>"
                                                aria-controls="vehicle-<?php echo e($val->id); ?>"
                                                aria-selected="<?php echo e($key === 0 ? 'true' : 'false'); ?>">
                                                <?php echo e($val->name . ' - ' . $val->en_name); ?>

                                            </button>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                                <div class="tab-content">
                                    <?php $__currentLoopData = $vehicle; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="tab-pane fade show <?php echo e($key === 0 ? 'active' : ''); ?>"
                                            id="vehicle-<?php echo e($val->id); ?>" role="tabpanel">
                                            <div class="nav-align-left mb-6">
                                                <ul class="nav nav-tabs me-4" role="tablist">
                                                    <?php $__currentLoopData = $val->types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $type_val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li class="nav-item">
                                                            <button type="button"
                                                                class="nav-link <?php echo e($type === 0 ? 'active' : ''); ?>"
                                                                role="tab" data-bs-toggle="tab"
                                                                data-bs-target="#type-<?php echo e($type_val->id); ?>"
                                                                aria-controls="type-<?php echo e($type_val->id); ?>"
                                                                aria-selected="true"><?php echo e($type_val->name . ' - ' . $type_val->en_name); ?></button>
                                                        </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                                <div class="tab-content ">
                                                    <?php $__currentLoopData = $val->types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $type_val): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <div class="tab-pane fade show <?php echo e($type === 0 ? 'active' : ''); ?>"
                                                            id="type-<?php echo e($type_val->id); ?>" role="tabpanel">
                                                            <?php $__currentLoopData = $type_val->sizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="form-check mb-2">
                                                                    <input type="checkbox"
                                                                        class="form-check-input size-checkbox"
                                                                        id="size_<?php echo e($size->id); ?>" name="sizes[]"
                                                                        value="<?php echo e($size->id); ?>">
                                                                    <label class="form-check-label fw-bold"
                                                                        for="size_<?php echo e($size->id); ?>">
                                                                        <?php echo e($size->name); ?>

                                                                    </label>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <span class="sizes-error text-danger text-error"></span>

                            </div>
                        </div>

                        <!-- Pricing Inputs -->
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text"><strong><?php echo e(__('Pricing')); ?></strong></div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label class="form-label"><?php echo e(__('Base Fare')); ?></label>
                                    <input type="number" name="base_fare" class="form-control" placeholder="0.00" />
                                    <span class="base_fare-error text-danger text-error"></span>

                                </div>
                                <div class="col-md-4">
                                    <label class="form-label"><?php echo e(__('Base Distance')); ?></label>
                                    <input type="number" name="base_distance" class="form-control" placeholder="km" />
                                    <span class="base_distance-error text-danger text-error"></span>

                                </div>
                                <div class="col-md-4">
                                    <label class="form-label"><?php echo e(__('Base Waiting')); ?></label>
                                    <input type="number" name="base_waiting" class="form-control"
                                        placeholder="minuets" />
                                    <span class="base_waiting-error text-danger text-error"></span>

                                </div>
                                <div class="col-md-4"></div>

                                <div class="col-md-4">
                                    <label class="form-label"><?php echo e(__('Distance Fare')); ?></label>
                                    <input type="number" name="distance_fare" min="0.00" class="form-control"
                                        placeholder="0.00" />
                                    <span class="distance_fare-error text-danger text-error"></span>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label"><?php echo e(__('Waiting Fare')); ?></label>
                                    <input type="number" name="waiting_fare" min="0.00" class="form-control"
                                        placeholder="0.00" />
                                    <span class="waiting_fare-error text-danger text-error"></span>

                                </div>
                            </div>


                        </div>

                        <!-- Customize Pricing -->
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text"><strong><?php echo e(__('Pricing Methods')); ?></strong></div>
                            </div>
                            <?php $__currentLoopData = $pricing_methods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input toggle-method"
                                        data-method-id="<?php echo e($method->id); ?>" data-method-type="<?php echo e($method->type); ?>"
                                        id="method_<?php echo e($method->id); ?>" name="methods[]" value="<?php echo e($method->id); ?>">
                                    <label class="form-check-label fw-bold" for="method_<?php echo e($method->id); ?>">
                                        <?php echo e($method->name); ?>

                                    </label>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <span class="methods-error text-danger text-error"></span>
                        </div>

                        <!-- Dynamic Pricing (جافاسكربت يضيف الحقول داخلها) -->
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text">
                                    <strong><?php echo e(__('Dynamic Pricing Based on Field Values')); ?></strong>
                                </div>
                            </div>
                            <div>
                                <div class="row g-2 mb-2 field-pricing-row">
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="button" class="btn btn-sm border add-field-pricing">
                                            <i class="ti ti-plus me-0 me-sm-1 ti-xs"></i> <?php echo e(__('add field')); ?>

                                        </button>
                                    </div>
                                </div>
                                <div id="field-pricing-wrapper"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text"><strong><?php echo e(__('Dynamic Pricing Based on Geo-fence')); ?></strong>
                                </div>
                            </div>

                            <button type="button" class="btn btn-sm border mb-2 add-geofence-pricing">
                                <i class="ti ti-plus me-0 me-sm-1 ti-xs"></i> <?php echo e(__('add geofence')); ?>

                            </button>

                            <div id="geofence-pricing-wrapper"></div>
                        </div>


                        <!-- Commission -->
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text"><strong><?php echo e(__('Commission')); ?></strong></div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label"><?php echo e(__('VAT Commission')); ?></label>
                                    <input type="number" name="vat_commission" class="form-control" min="0.00"
                                        placeholder="0.00">
                                    <span class="vat_commission-error text-danger text-error"></span>

                                </div>
                                <div class="col-md-6">
                                    <!-- Service Commission Status Switch -->
                                    <div class="mb-3">
                                        <label class="form-label"><?php echo e(__('Service Commission Status')); ?></label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox"
                                                id="service_commission_status" name="service_commission_status"
                                                value="1" checked>
                                            <label class="form-check-label" for="service_commission_status">
                                                <?php echo e(__('Enable Service Commission')); ?>

                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Service Commission Fields (shown when switch is enabled) -->
                            <div id="service_commission_fields" class="row">
                                <div class="col-md-6">
                                    <label class="form-label"><?php echo e(__('Service Commission Type')); ?></label>
                                    <select name="service_commission_type" class="form-select"
                                        id="service_commission_type">
                                        <option value="percentage"><?php echo e(__('Percentage')); ?></option>
                                        <option value="fixed"><?php echo e(__('Fixed Amount')); ?></option>
                                    </select>
                                    <span class="service_commission_type-error text-danger text-error"></span>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"
                                        id="service_commission_label"><?php echo e(__('Service Tax Commission (%)')); ?></label>
                                    <input type="number" name="service_commission" class="form-control" min="0.00"
                                        placeholder="0.00" id="service_commission_input">
                                    <span class="service_commission-error text-danger text-error"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Discount -->
                        <div class="mb-3">
                            <div class="divider text-start">
                                <div class="divider-text"><strong><?php echo e(__('Discount Fare')); ?></strong></div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label"><?php echo e(__('Discount percentage %')); ?></label>
                                <input type="number" name="discount" class="form-control" min="0.00"
                                    placeholder="0.00">
                                <span class="discount-error text-danger text-error"></span>
                            </div>
                        </div>

                    </div>

                    <!-- Footer -->
                    <div class="modal-footer">
                        <button type="button" class="btn btn-label-secondary"
                            data-bs-dismiss="modal"><?php echo e(__('Close')); ?></button>
                        <button type="submit" class="btn btn-primary me-3 data-submit"><?php echo e(__('Submit')); ?></button>
                    </div>
                </form>

            </div>
        </div>
    </div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts/layoutMaster', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\safedest\resources\views/admin/settings/templates/edit.blade.php ENDPATH**/ ?>